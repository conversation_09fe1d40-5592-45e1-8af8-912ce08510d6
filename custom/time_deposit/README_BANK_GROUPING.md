# Time Deposit Report - Bank Grouping Logic

## Overview
Time Deposit Excel Report akan membuat multiple sheets berdasarkan bank disbursement account yang digunakan dalam setiap deposit.

## Logika Grouping

### 1. **Deposito Bank Konvensional IDR**
- **Kriteria**: Bank account mengandung kata "BNI"
- **Contoh**: "BANK DISBURSEMENT BNI", "BNI SYARIAH", dll
- **Sheet Name**: "Deposito Bank Konvensional IDR"

### 2. **Deposito Bank Konvensional Merchant**  
- **Kriteria**: Bank account mengandung kata "BRI"
- **Contoh**: "BANK DISBURSEMENT BRI", "BRI SYARIAH", dll
- **Sheet Name**: "Deposito Bank Konvensional Merchant"

### 3. **Deposito Bank Syariah**
- **Kriteria**: Product type = "syariah" (tidak peduli bank account)
- **Sheet Name**: "Deposito Bank Syariah"

### 4. **Deposito USD**
- **Kriteria**: Deposit type = "mma" (tidak peduli bank account atau product)
- **Sheet Name**: "Deposito USD"

### 5. **Deposito Lainnya**
- **Kriteria**: Semua deposit yang tidak masuk kategori di atas
- **Sheet Name**: "Deposito Lainnya"

## Implementasi

### Method `_get_bank_category(deposit)`
```python
def _get_bank_category(self, deposit):
    bank_account = deposit.bank_account or ''
    
    if 'BNI' in bank_account.upper():
        return 'bni'  # Konvensional IDR
    elif 'BRI' in bank_account.upper():
        return 'bri'  # Konvensional Merchant
    elif deposit.deposit_product and deposit.deposit_product.lower() == 'syariah':
        return 'syariah'  # Syariah
    elif deposit.deposit_type == 'mma':
        return 'usd'  # USD
    else:
        return 'other'  # Lainnya
```

## Contoh Hasil Excel

Ketika filter **Product = "All"** dan **Deposit Type = "All"**:

```
📁 time_deposit_report_YYYYMMDD_HHMMSS.xlsx
├── 📄 Deposito Bank Konvensional IDR (BNI accounts)
├── 📄 Deposito Bank Konvensional Merchant (BRI accounts)
├── 📄 Deposito Bank Syariah (product=syariah)
├── 📄 Deposito USD (type=mma)
└── 📄 Deposito Lainnya (other cases)
```

## Field Mapping

Setiap sheet akan berisi kolom yang sama:
- No
- Deposit Number  
- Open Date
- Mature Date
- Deposit In Days
- Bank
- Amount
- Interest Rate
- Beneficiary Bank
- Bank Account ← **Field utama untuk grouping**
- Type
- No Bilyet Deposito
- Classification
- Remarks
- Breakable %
- Deposit Status

## Testing

1. Buat data time deposit dengan bank account yang berbeda:
   - "BANK DISBURSEMENT BNI" → masuk sheet Konvensional IDR
   - "BANK DISBURSEMENT BRI" → masuk sheet Konvensional Merchant
   - Product "syariah" → masuk sheet Syariah
   - Type "mma" → masuk sheet USD

2. Generate report dengan filter All/All
3. Verifikasi data terbagi dengan benar ke sheet masing-masing
