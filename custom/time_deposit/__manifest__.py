# -*- coding: utf-8 -*-
{
    'name': "time_deposit",

    'summary': "Short (1 phrase/line) summary of the module's purpose",

    'description': """
Long description of module's purpose
    """,

    'author': "My Company",
    'website': "https://www.yourcompany.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base', 'mail', 'account', 'hr_extension', 'account_inherit','invoicing_period'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'data/ir_sequence.xml',
        'views/account_move.xml',
        'views/time_deposit_views.xml',
        'wizard/time_deposit_report_wizard_views.xml',
        'views/menuitem.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'time_deposit/static/src/css/time_deposit_report.css',
            'time_deposit/static/src/js/time_deposit_report_dialog.js',
        ],
    },
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
}

