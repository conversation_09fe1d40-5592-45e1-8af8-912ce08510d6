/** @odoo-module **/

import { registry } from "@web/core/registry";

// Service to handle Time Deposit Report dialog behavior
const timeDepositReportService = {
    start() {
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', this.initializeDialogHandler.bind(this));
        // Also handle dynamically created dialogs
        this.observeDialogs();
    },

    observeDialogs() {
        // Create a MutationObserver to watch for new modal dialogs
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if it's a modal with Time Deposit Report title
                        const modal = node.querySelector('.modal[aria-labelledby*="Time Deposit Report"]') ||
                                     (node.classList && node.classList.contains('modal') &&
                                      node.getAttribute('aria-labelledby') &&
                                      node.getAttribute('aria-labelledby').includes('Time Deposit Report')) ? node : null;

                        if (modal) {
                            this.disableModalDragging(modal);
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    },

    initializeDialogHandler() {
        // Handle existing dialogs
        const existingModals = document.querySelectorAll('.modal[aria-labelledby*="Time Deposit Report"]');
        existingModals.forEach(modal => this.disableModalDragging(modal));
    },

    disableModalDragging(modal) {
        const modalDialog = modal.querySelector('.modal-dialog');
        const modalHeader = modal.querySelector('.modal-header');
        const modalTitle = modal.querySelector('.modal-title');

        if (modalDialog && modalHeader) {
            // Disable dragging events
            modalHeader.style.cursor = 'default';
            modalHeader.draggable = false;

            // Prevent mouse events that could trigger dragging
            modalHeader.addEventListener('mousedown', (e) => {
                e.preventDefault();
                e.stopPropagation();
                return false;
            });

            modalHeader.addEventListener('dragstart', (e) => {
                e.preventDefault();
                return false;
            });

            if (modalTitle) {
                modalTitle.style.cursor = 'default';
                modalTitle.draggable = false;
                modalTitle.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                });
            }

            // Fix modal position
            modalDialog.style.position = 'fixed';
            modalDialog.style.top = '50%';
            modalDialog.style.left = '50%';
            modalDialog.style.transform = 'translate(-50%, -50%)';
            modalDialog.style.margin = '0';
        }
    }
};

// Register the service
registry.category("services").add("timeDepositReportService", timeDepositReportService);
