/** @odoo-module **/

// Simple approach to handle Time Deposit Report dialog
document.addEventListener('DOMContentLoaded', function() {
    // Use MutationObserver to watch for new modal dialogs
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if it's a modal with Time Deposit Report title
                    let modal = null;

                    if (node.classList && node.classList.contains('modal')) {
                        const title = node.querySelector('.modal-title');
                        if (title && title.textContent.includes('Time Deposit Report')) {
                            modal = node;
                        }
                    } else {
                        modal = node.querySelector('.modal .modal-title');
                        if (modal && modal.textContent.includes('Time Deposit Report')) {
                            modal = node.querySelector('.modal');
                        } else {
                            modal = null;
                        }
                    }

                    if (modal) {
                        disableModalDragging(modal);
                    }
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

function disableModalDragging(modal) {
    const modalDialog = modal.querySelector('.modal-dialog');
    const modalHeader = modal.querySelector('.modal-header');
    const modalTitle = modal.querySelector('.modal-title');

    if (modalDialog && modalHeader) {
        // Disable dragging events
        modalHeader.style.cursor = 'default';
        modalHeader.draggable = false;

        // Prevent mouse events that could trigger dragging
        modalHeader.addEventListener('mousedown', function(e) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        });

        modalHeader.addEventListener('dragstart', function(e) {
            e.preventDefault();
            return false;
        });

        if (modalTitle) {
            modalTitle.style.cursor = 'default';
            modalTitle.draggable = false;
            modalTitle.addEventListener('mousedown', function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            });
        }

        // Fix modal position
        modalDialog.style.position = 'fixed';
        modalDialog.style.top = '50%';
        modalDialog.style.left = '50%';
        modalDialog.style.transform = 'translate(-50%, -50%)';
        modalDialog.style.margin = '0';
    }
}
