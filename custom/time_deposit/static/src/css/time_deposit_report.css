/* Time Deposit Report Wizard Styling */

/* Target modal dialogs with Time Deposit Report title */
.modal[aria-labelledby*="Time Deposit Report"] .modal-dialog {
    max-width: 600px !important;
    width: 90% !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
}

/* Disable dragging on modal header */
.modal[aria-labelledby*="Time Deposit Report"] .modal-header {
    cursor: default !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #007bff !important;
}

/* Prevent dragging on title */
.modal[aria-labelledby*="Time Deposit Report"] .modal-title {
    cursor: default !important;
    pointer-events: none !important;
    color: #007bff !important;
    font-weight: bold !important;
}

/* Style the form header */
.modal[aria-labelledby*="Time Deposit Report"] .o_form_view header h3 {
    color: #2c3e50;
    font-weight: bold;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

/* Style the form content */
.modal[aria-labelledby*="Time Deposit Report"] .o_form_view {
    padding: 20px;
}

/* Style the group */
.modal[aria-labelledby*="Time Deposit Report"] .o_group {
    margin-bottom: 20px;
}

/* Style form fields */
.modal[aria-labelledby*="Time Deposit Report"] .o_field_widget {
    margin-bottom: 10px;
}

/* Style buttons */
.modal[aria-labelledby*="Time Deposit Report"] .modal-footer .btn-primary {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    padding: 10px 20px;
    font-weight: bold;
}

.modal[aria-labelledby*="Time Deposit Report"] .modal-footer .btn-secondary {
    padding: 10px 20px;
    margin-left: 10px;
}

/* Disable modal dragging completely */
.modal[aria-labelledby*="Time Deposit Report"] {
    pointer-events: auto !important;
}

.modal[aria-labelledby*="Time Deposit Report"] .modal-dialog {
    pointer-events: auto !important;
}

.modal[aria-labelledby*="Time Deposit Report"] .modal-content {
    pointer-events: auto !important;
}
