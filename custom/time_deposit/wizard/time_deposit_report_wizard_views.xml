<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Time Deposit Report Wizard Form View -->
        <record id="view_time_deposit_report_wizard_form" model="ir.ui.view">
            <field name="name">time.deposit.report.wizard.form</field>
            <field name="model">time.deposit.report.wizard</field>
            <field name="arch" type="xml">
                <form string="Time Deposit Report" create="false" edit="false" delete="false">
                    <header>
                        <h3 class="text-center mb-3">Time Deposit Report Filter</h3>
                    </header>
                    <sheet>
                        <group>
                            <group string="Filter Options" class="col-12">
                                <field name="business_unit_id" options="{'no_create': True, 'no_edit': True}" required="1"/>
                                <field name="date_from" required="1"/>
                                <field name="date_to" required="1"/>
                                <field name="product" required="1"/>
                                <field name="deposit_type" required="1"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_generate_excel_report"
                                string="Generate Excel Report"
                                type="object"
                                class="btn-primary"
                                icon="fa-file-excel-o"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" icon="fa-times"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Time Deposit Report Wizard Action -->
        <record id="action_time_deposit_report_wizard" model="ir.actions.act_window">
            <field name="name">Time Deposit Report</field>
            <field name="res_model">time.deposit.report.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="view_time_deposit_report_wizard_form"/>
            <field name="context">{
                'dialog_size': 'medium',
                'default_focus_field': 'business_unit_id',
                'form_view_initial_mode': 'edit'
            }</field>
        </record>

    </data>
</odoo>
