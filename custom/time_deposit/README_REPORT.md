# Time Deposit Report Module

## Overview
Modul ini menambahkan fitur report Excel untuk Time Deposit dengan filter yang dapat disesuaikan.

## Fitur Baru
1. **Wizard Filter Report** - Form filter untuk memilih kriteria report (dialog tidak bisa digeser)
2. **Export Excel** - Generate report dalam format Excel (.xlsx)
3. **Menu Report** - Menu khusus untuk mengakses report dengan struktur hierarki
4. **Fixed Dialog** - Dialog wizard tidak bisa digeser-geser untuk UX yang lebih baik

## Cara Menggunakan

### 1. Akses Menu Report
- Buka menu **Time Deposit > Reporting > Time Deposit Report**
- Akan muncul wizard filter

### 2. Atur Filter
- **Business Unit**: Pilih unit bisnis (default: company saat ini)
- **Date From**: Tanggal mulai (default: awal bulan ini)
- **Date To**: Tanggal akhir (default: hari ini)
- **Product**: <PERSON><PERSON>h produk (All/Syariah/Konven)
- **Deposit Type**: Pilih tipe deposit (All/Corporate Credit Card/DOC/Deposit/MMA/Optima)

### 3. Generate Report
- Klik tombol **"Generate Excel Report"**
- File Excel akan otomatis terdownload

## Format Report Excel

Report Excel terdiri dari **multiple sheets** berdasarkan deposit type:

### Sheets yang Dibuat:
1. **Deposito Bank Kabupaten Unit** (deposit_type: doc)
2. **Deposito Bank Kabupaten Merchant** (deposit_type: ccredit)
3. **Deposito Bank Syariah** (deposit_type: deposit)
4. **Deposito USD** (deposit_type: mma)
5. **Deposito Optima** (deposit_type: optima)

### Kolom dalam setiap sheet:
1. No
2. Deposit Number
3. Open Date
4. Mature Date
5. Deposit In Days
6. Bank
7. Amount
8. Interest Rate
9. Beneficiary Bank
10. Bank Account
11. Type
12. No Bilyet Deposito (kosong untuk sementara)
13. Classification
14. Remarks
15. Breakable % (kosong untuk sementara)
16. Deposit Status

**Catatan:** Setiap sheet hanya berisi data untuk deposit type yang sesuai dengan nama sheet tersebut.

## Technical Details

### Files Added/Modified:
- `wizard/time_deposit_report_wizard.py` - Wizard model untuk filter
- `wizard/time_deposit_report_wizard_views.xml` - View untuk wizard (fixed dialog)
- `controllers/time_deposit_report_controller.py` - Controller untuk generate Excel
- `views/menuitem.xml` - Struktur menu hierarki (Time Deposit > Reporting > Report)
- `security/ir.model.access.csv` - Access rights untuk wizard
- `static/src/css/time_deposit_report.css` - Styling untuk fixed dialog
- `static/src/js/time_deposit_report_dialog.js` - JavaScript untuk disable dragging

### Dependencies:
- xlsxwriter (untuk generate Excel)
- Modul time_deposit yang sudah ada

### URL Endpoint:
- `/time_deposit/excel_report` - Endpoint untuk download Excel report
