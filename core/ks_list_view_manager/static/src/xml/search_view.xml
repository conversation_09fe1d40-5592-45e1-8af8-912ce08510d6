<?xml version="1.0" encoding="UTF-8" ?>
<templates>
    <t t-name="ks_general_advance_search_view" t-inherit="web.ListRenderer" t-inherit-mode="extension" owl="1">
        <xpath expr="//table/thead/tr" position="after">
            <t t-if="!this.isX2Many">
                <t t-call="ks_general_template"/>
            </t>
        </xpath>
    </t>
    <!--    we have used td tag instead of th tag because of one custom column
     hook of odoo default because in that hook they  selected the thead
     th tag which causes problem in calculating the width-->
    <t t-name="ks_general_template">
        <tr class="hide-on-modal">
            <t t-if="hasSelectors">
                <th></th>
            </t>
            <t t-if="ks_serial_number">
                <th></th>
            </t>
            <t t-foreach="columns" t-as="column" t-key="column_index">
                <t t-if="(props.list.fields[column.name] !== undefined)">
                    <t t-if="(props.list.fields[column.name].store === true and column.name != 'sequence'
                    and props.list.fields[column.name].type != 'one2many' and props.list.fields[column.name].type != 'boolean')">
                        <th class="ks_advance_search_row ks_dropdown_scroll">
                            <SearchView
                                    ks_field_id="props.list.fields[column.name].name"
                                    ks_description="column.string || props.list.fields[column.name].string"
                                    ks_field_type="props.list.fields[column.name].type"
                                    ks_selection_values="props.list.fields[column.name].selection ? props.list.fields[column.name].selection :[]"
                                    model="props.list.resModel"
                                    ks_search_event="(options)=>this.Ks_update_advance_search_controller(options)"
                                    ks_field_search_info="ks_field_popup"
                                    ks_remove_search="(ev)=>this.ks_remove_popup_domain_event(ev,props.list.fields[column.name].type,props.list.fields[column.name].name)"
                            />
                        </th>
                    </t>
                    <t t-else="">
                        <th class="ks_advance_search_row">
                        </th>
                    </t>
                </t>
                <t t-else="">
                        <th class="ks_advance_search_row">
                        </th>
                </t>

            </t>
        </tr>
    </t>
</templates>