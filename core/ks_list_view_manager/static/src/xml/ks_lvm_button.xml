<?xml version="1.0" encoding="UTF-8"?>
<template xml:space="preserve">
    <t t-name="ks_ListView.buttons">
            <div id="ks_dropdown" class="dropdown">
                <button type="button" class="btn btn-primary toggle_button dropdown-toggle o_button_lvm ml-1"
                        data-bs-toggle="dropdown" title="Dynamic List Settings"
                        aria-expanded="false">
                    <span class="fa fa-lg fa fa-cog"/>

                    <span class="caret"/>
                </button>
<!--                <div class="ks_lvm_dd">-->
                    <ul class="dropdown-menu ks_columns ks_list_view_dropdown_container">
                        <div class="d-flex mb-2">
                            <input type="text" id="myInput" placeholder="Search for Columns.." class="myinput"
                                   title="Search For Columns"
                                   autocomplete="off"   t-on-keyup="ks_searchBar" />

                            <button type="button" class="btn btn-primary restore_button o_button_lvm"
                                    title="Restore to Default View">
                                <span class="fa fa-lg fa fa-undo" t-on-click="ks_confirm_restoreData"/>
                            </button>
                        </div>

                        <t t-call="ks_list_view_fields_selection_list"/>

                    </ul>
<!--                </div>-->

            </div>

            <button type="button" class="btn btn-primary refresh_button o_button_lvm mx-1" title="Refresh Data" t-on-click="ks_reload_list_view">
                <span class="fa fa-lg fa fa-refresh"/>
            </button>
            <button type="button" class="btn btn-primary copy_button o_button_lvm mr-1" style="display : none; margin-right:4px;" title="Duplicate Records" t-on-click="_onKsDuplicateRecord" t-att-checked="this.ks_table_data.ks_editable">
                <span class="fa fa-copy"/>
            </button>
            <label class="switch mr-1 mode_button o_button_lvm" data-toggle="tooltip" title="Read/Edit Mode">
                <input type="checkbox" id="mode" class="toggle_change" t-on-click="ks_modeToggle" t-att-checked="this.ks_table_data.ks_editable"/>
                <span class="slider round"/>
            </label>
        </t>
</template>
